import { ApiProperty } from '@nestjs/swagger';
import { IsE<PERSON>, IsNotEmpty, IsString, <PERSON><PERSON><PERSON>th, <PERSON><PERSON>ptional, IsArray } from 'class-validator';

export class RegisterDto {
  @ApiProperty({
    description: 'User full name',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'User email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'User password',
    example: 'password123',
    minLength: 6,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;

  @ApiProperty({
    description: 'User roles',
    example: ['user'],
    required: false,
  })
  @IsArray()
  @IsOptional()
  roles?: string[];
}

import { SetMetadata } from '@nestjs/common';

export interface PermissionRequirement {
  resource: string;
  action: string;
  resourceIdParam?: string; // URL parameter name for resource ID
}

export const PERMISSIONS_KEY = 'permissions';

export const RequirePermissions = (...permissions: PermissionRequirement[]) =>
  SetMetadata(PERMISSIONS_KEY, permissions);

// Convenience decorators for common permissions
export const CanView = (resource: string, resourceIdParam?: string) =>
  RequirePermissions({ resource, action: 'view', resourceIdParam });

export const CanEdit = (resource: string, resourceIdParam?: string) =>
  RequirePermissions({ resource, action: 'edit', resourceIdParam });

export const CanDelete = (resource: string, resourceIdParam?: string) =>
  RequirePermissions({ resource, action: 'delete', resourceIdParam });

export const CanManage = (resource: string, resourceIdParam?: string) =>
  RequirePermissions({ resource, action: 'manage', resourceIdParam });

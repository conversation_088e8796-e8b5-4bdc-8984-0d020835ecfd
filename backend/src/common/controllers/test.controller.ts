import { Controller, Get, UseGuards, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { PermissionsGuard } from '../guards/permissions.guard';
import { AdminOnly, UserOrAdmin } from '../decorators/roles.decorator';
import { CanView, CanEdit, CanManage } from '../decorators/permissions.decorator';

@ApiTags('Test Authorization')
@Controller('test')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TestController {
  
  @Get('public')
  @ApiOperation({ summary: 'Public endpoint (authentication required)' })
  @ApiResponse({ status: 200, description: 'Success' })
  getPublic() {
    return { message: 'This is a public endpoint (but requires authentication)' };
  }

  @Get('admin-only')
  @UseGuards(RolesGuard)
  @AdminOnly()
  @ApiOperation({ summary: 'Admin only endpoint' })
  @ApiResponse({ status: 200, description: 'Success' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  getAdminOnly() {
    return { message: 'This endpoint is only for admins!' };
  }

  @Get('user-or-admin')
  @UseGuards(RolesGuard)
  @UserOrAdmin()
  @ApiOperation({ summary: 'User or Admin endpoint' })
  @ApiResponse({ status: 200, description: 'Success' })
  @ApiResponse({ status: 403, description: 'Forbidden - User or Admin role required' })
  getUserOrAdmin() {
    return { message: 'This endpoint is for users or admins!' };
  }

  @Get('documents/:id/view')
  @UseGuards(PermissionsGuard)
  @CanView('document', 'id')
  @ApiOperation({ summary: 'View document (permission required)' })
  @ApiResponse({ status: 200, description: 'Success' })
  @ApiResponse({ status: 403, description: 'Forbidden - View permission required' })
  viewDocument(@Param('id') id: string) {
    return { message: `Viewing document ${id}` };
  }

  @Get('documents/:id/edit')
  @UseGuards(PermissionsGuard)
  @CanEdit('document', 'id')
  @ApiOperation({ summary: 'Edit document (permission required)' })
  @ApiResponse({ status: 200, description: 'Success' })
  @ApiResponse({ status: 403, description: 'Forbidden - Edit permission required' })
  editDocument(@Param('id') id: string) {
    return { message: `Editing document ${id}` };
  }

  @Get('organizations/:id/manage')
  @UseGuards(PermissionsGuard)
  @CanManage('organization', 'id')
  @ApiOperation({ summary: 'Manage organization (permission required)' })
  @ApiResponse({ status: 200, description: 'Success' })
  @ApiResponse({ status: 403, description: 'Forbidden - Manage permission required' })
  manageOrganization(@Param('id') id: string) {
    return { message: `Managing organization ${id}` };
  }
}

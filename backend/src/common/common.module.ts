import { Module } from '@nestjs/common';
import { PermissionsModule } from '../permissions/permissions.module';
import { TestController } from './controllers/test.controller';
import { PermissionsGuard } from './guards/permissions.guard';
import { RolesGuard } from './guards/roles.guard';

@Module({
  imports: [PermissionsModule],
  controllers: [TestController],
  providers: [PermissionsGuard, RolesGuard],
  exports: [PermissionsGuard, RolesGuard],
})
export class CommonModule {}

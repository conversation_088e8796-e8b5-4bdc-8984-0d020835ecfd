import { Controller, Post, Body, UseGuards, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PermissionsService } from '../services/permissions.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';

@ApiTags('Permissions')
@Controller('permissions')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PermissionsController {
  constructor(private readonly permissionsService: PermissionsService) {}

  @Get('check')
  @ApiOperation({ summary: 'Check user permission' })
  @ApiResponse({ status: 200, description: 'Permission check result' })
  async checkPermission(
    @Query('userId') userId: string,
    @Query('resource') resource: string,
    @Query('resourceId') resourceId: string,
    @Query('permission') permission: string,
  ) {
    const hasPermission = await this.permissionsService.checkPermission(
      userId,
      resource,
      resourceId,
      permission,
    );

    return {
      hasPermission,
      userId,
      resource,
      resourceId,
      permission,
    };
  }

  @Post('relationship')
  @ApiOperation({ summary: 'Create permission relationship' })
  @ApiResponse({ status: 201, description: 'Relationship created successfully' })
  async createRelationship(
    @Body()
    body: {
      resourceType: string;
      resourceId: string;
      relation: string;
      subjectType: string;
      subjectId: string;
    },
  ) {
    const result = await this.permissionsService.createRelationship(
      body.resourceType,
      body.resourceId,
      body.relation,
      body.subjectType,
      body.subjectId,
    );

    return {
      success: result,
      relationship: `${body.resourceType}:${body.resourceId}#${body.relation}@${body.subjectType}:${body.subjectId}`,
    };
  }
}

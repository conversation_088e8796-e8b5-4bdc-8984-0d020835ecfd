import { v1 } from '@authzed/authzed-node'
import { Injectable, Logger, type OnModuleInit } from '@nestjs/common'
import type { ConfigService } from '@nestjs/config'

@Injectable()
export class SpiceDbService implements OnModuleInit {
  private readonly logger = new Logger(SpiceDbService.name)
  private client: v1.ZedServiceClient | null = null

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    try {
      const endpoint = this.configService.get<string>('SPICEDB_ENDPOINT', 'localhost:50051')
      const token = this.configService.get<string>('SPICEDB_TOKEN', 'somerandomkeyhere')

      this.client = v1.NewClient(token, endpoint, v1.ClientSecurity.INSECURE_PLAINTEXT_CREDENTIALS)

      this.logger.log(`SpiceDB client initialized with endpoint: ${endpoint}`)

      // Test connection
      await this.testConnection()
    } catch (error) {
      this.logger.error('Failed to initialize SpiceDB client:', error)
    }
  }

  private async testConnection() {
    try {
      const response = await this.client.readSchema({})
      this.logger.log('SpiceDB connection successful')
      return true
    } catch (error) {
      this.logger.warn('SpiceDB connection failed, using fallback mode:', error.message)
      return false
    }
  }

  async writeSchema(schema: string): Promise<boolean> {
    try {
      await this.client.writeSchema({
        schema: schema,
      })
      this.logger.log('Schema written successfully')
      return true
    } catch (error) {
      this.logger.error('Failed to write schema:', error)
      return false
    }
  }

  async readSchema(): Promise<string | null> {
    try {
      const response = await this.client.readSchema({})
      return response.schemaText
    } catch (error) {
      this.logger.error('Failed to read schema:', error)
      return null
    }
  }

  async createRelationship(
    resourceType: string,
    resourceId: string,
    relation: string,
    subjectType: string,
    subjectId: string,
  ): Promise<boolean> {
    try {
      await this.client.writeRelationships({
        updates: [
          {
            operation: v1.RelationshipUpdate_Operation.CREATE,
            relationship: {
              resource: {
                objectType: resourceType,
                objectId: resourceId,
              },
              relation: relation,
              subject: {
                object: {
                  objectType: subjectType,
                  objectId: subjectId,
                },
              },
            },
          },
        ],
      })

      this.logger.log(`Relationship created: ${resourceType}:${resourceId}#${relation}@${subjectType}:${subjectId}`)
      return true
    } catch (error) {
      this.logger.error('Failed to create relationship:', error)
      return false
    }
  }

  async checkPermission(
    resourceType: string,
    resourceId: string,
    permission: string,
    subjectType: string,
    subjectId: string,
  ): Promise<boolean> {
    try {
      const response = await this.client.checkPermission({
        resource: {
          objectType: resourceType,
          objectId: resourceId,
        },
        permission: permission,
        subject: {
          object: {
            objectType: subjectType,
            objectId: subjectId,
          },
        },
      })

      const hasPermission = response.permissionship === v1.CheckPermissionResponse_Permissionship.HAS_PERMISSION

      this.logger.log(
        `Permission check: ${subjectType}:${subjectId} ${hasPermission ? 'HAS' : 'DOES NOT HAVE'} ${permission} on ${resourceType}:${resourceId}`,
      )

      return hasPermission
    } catch (error) {
      this.logger.error('Failed to check permission:', error)
      return false
    }
  }

  async deleteRelationship(
    resourceType: string,
    resourceId: string,
    relation: string,
    subjectType: string,
    subjectId: string,
  ): Promise<boolean> {
    try {
      await this.client.writeRelationships({
        updates: [
          {
            operation: v1.RelationshipUpdate_Operation.DELETE,
            relationship: {
              resource: {
                objectType: resourceType,
                objectId: resourceId,
              },
              relation: relation,
              subject: {
                object: {
                  objectType: subjectType,
                  objectId: subjectId,
                },
              },
            },
          },
        ],
      })

      this.logger.log(`Relationship deleted: ${resourceType}:${resourceId}#${relation}@${subjectType}:${subjectId}`)
      return true
    } catch (error) {
      this.logger.error('Failed to delete relationship:', error)
      return false
    }
  }
}

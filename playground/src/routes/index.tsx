import { Button, Text } from '@massimo-erp/components/shared'
import { clsx } from '@massimo-erp/helpers'
import { componentSections } from '../data/components'

export default function Home() {
  return (
    <div class={clsx('min-h-screen w-screen', 'px-4 py-12')}>
      <div class={clsx('mx-auto max-w-7xl', 'space-y-12')}>
        <div class={clsx('text-center', 'space-y-4')}>
          <Text
            as="p"
            size="2xl"
            thickness="bold"
            class={clsx('inline-block', 'bg-black/10', 'px-8 py-4', 'rounded-max')}>
            Massimo ERP Playground
          </Text>
          <p class="text-gray-600 text-lg">Explore and test all available components</p>
        </div>
        <div class={clsx('grid gap-12', 'lg:grid-cols-2 xl:grid-cols-3')}>
          {componentSections.map(section => (
            <div class={clsx('space-y-6')}>
              <Text
                as="p"
                size="lg"
                class={clsx('text-center', 'border-b', 'border-black/10', 'pb-3')}>
                {section.title}
              </Text>
              <div class={clsx('grid gap-3', 'sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-1')}>
                {section.components.map(component => (
                  <a
                    href={component.href}
                    class="block">
                    <Button class={clsx('w-full', 'justify-center')}>{component.name}</Button>
                  </a>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

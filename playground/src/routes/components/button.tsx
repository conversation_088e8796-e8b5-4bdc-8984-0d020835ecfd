import { Button, Text } from '@massimo-erp/components/shared'
import { clsx } from '@massimo-erp/helpers'
import { createSignal } from 'solid-js'

export default function Home() {
  const [counter, setCounter] = createSignal(0)

  return (
    <main
      class={clsx(
        'min-h-screen', // sizing
        'flex flex-col items-center justify-center', // flex
      )}>
      <h1 class="text-3xl font-bold underline">Hello world!</h1>
      <Text
        as="label"
        for="sa"
        preset="normal">
        Counter: {counter()}
      </Text>
      <br />
      <Button
        class="mt-4"
        onClick={() => setCounter(counter() + 1)}>
        Increment
      </Button>
    </main>
  )
}

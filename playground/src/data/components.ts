export interface ComponentSection {
  title: string
  components: Array<{
    name: string
    href: string
  }>
}

export const componentSections: ComponentSection[] = [
  {
    title: 'Base Components',
    components: [
      {
        name: 'Button',
        href: '/components/button',
      },
      {
        name: 'Text',
        href: '/components/text',
      },
    ],
  },
  {
    title: 'Complex Components',
    components: [
      {
        name: 'Blurry Balls Background',
        href: '/components/blurry-balls',
      },
    ],
  },
]

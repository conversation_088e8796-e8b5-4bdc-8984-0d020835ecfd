# Rules

These are some rules we use to develop this project. 
They are set in stone and are subject to change.


!!! NO PUSH IN DEVELOPMENT BRANCH !!!

Every change should be made in a new branch and then send a pull request.
Urgent changes can be made in the without an issue.
All other changes should be made after an issue is created.


## Naming
- All file names should be `kebab-case` and end with `.ts` or `.tsx`
- Constants should be `SCREAMING_SNAKE_CASE`
- All other variables should be `camelCase`
- All functions should be `camelCase`
- All interfaces should be `PascalCase`
- All types should be `PascalCase`
- All components should be `PascalCase`
- All props should be `camelCase`
- All events should be `camelCase`
- branch names should be `kebab-case` and start with `feature/`, `fix/`, `chore/` or `refactor/`, `urgent/`

{
  "workspace": [
    "packages/*",
    "client",
    "playground"
  ],
  "imports": {
    "@biomejs/biome": "npm:@biomejs/biome@2.2.4",
    "vite-plugin-solid-svg": "npm:vite-plugin-solid-svg@^0.8.1"
  },
  "nodeModulesDir": "manual",
  "vendor": true,
  "compilerOptions": {
    /* Base Options: */
    "exactOptionalPropertyTypes": true,
    "lib": [
      "esnext"
    ],
    "noImplicitAny": true,
    "noUncheckedIndexedAccess": true,
    "skipLibCheck": true,
    /* Strictness */
    "strict": true,
    "strictNullChecks": true,
    "verbatimModuleSyntax": true
  },

  "tasks": {
    "check": "biome check --no-errors-on-unmatched --files-ignore-unknown=true .",
    "lint": "biome check --write --no-errors-on-unmatched --files-ignore-unknown=true .",
    "lint:unsafe": "biome check --write --no-errors-on-unmatched --files-ignore-unknown=true --unsafe .",

    "client:dev": "DENO_FUTURE=1 deno task --config client/deno.jsonc dev",
    "client:build": "deno task --config client/deno.jsonc build",
    "client:start": "deno task --config client/deno.jsonc start",
    "client:run": "deno task client:build && deno task client:start",

    "playground:dev": "DENO_FUTURE=1 deno task --config playground/deno.jsonc dev",
    "playground:build": "deno task --config playground/deno.jsonc build",
    "playground:start": "deno task --config playground/deno.jsonc start",
    "playground:run": "deno task playground:build && deno task playground:start",

    "clean": "./scripts/clean.sh",
    "purge": "./scripts/purge.sh",

    "install": "deno install --allow-scripts=npm:@parcel/watcher,npm:@tailwindcss/oxide"
  }
}

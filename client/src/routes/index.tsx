import { Button, Text } from '@massimo-erp/components/shared'
import { clsx, useGlobalStore } from '@massimo-erp/helpers'
import { createSignal } from 'solid-js'

export default function Home() {
  const [counter, setCounter] = createSignal(0)
  const globalStore = useGlobalStore()

  return (
    <main
      class={clsx(
        'min-h-screen', // sizing
        'flex flex-col items-center justify-center', // flex
      )}>
      <h1 class="text-3xl font-bold underline">Hello world!</h1>
      <Text
        as="label"
        for="sa"
        preset="normal">
        Counter: {counter()}
        <br />
      </Text>
      <Button
        class="mt-4"
        onClick={() => {
          setCounter(counter() + 1)
          globalStore.toggleTheme()
        }}>
        Increment
      </Button>
      <p>
        <br />
        Theme: {globalStore.theme}
        <br />
        Lang: {globalStore.lang}
      </p>
    </main>
  )
}

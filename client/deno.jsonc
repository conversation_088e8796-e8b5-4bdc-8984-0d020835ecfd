{"imports": {"~/": "./src/", "@deno/vite-plugin": "npm:@deno/vite-plugin", "@solidjs/start": "npm:@solidjs/start@latest", "@solidjs/router": "npm:@solidjs/router@latest", "solid-js": "npm:solid-js@latest", "vinxi": "npm:vinxi@latest", "tailwindcss": "npm:tailwindcss@^4.1.13", "@tailwindcss/vite": "npm:@tailwindcss/vite@^4.1.13", "vite-plugin-solid-svg": "npm:vite-plugin-solid-svg@~0.8.1", "@solidjs/meta": "npm:@solidjs/meta@latest", "@solid-primitives/cookies": "npm:@solid-primitives/cookies@^0.0.2"}, "compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "preserve", "jsxImportSource": "solid-js", "allowJs": true, "noEmit": true, "strict": true, "lib": ["deno.ns", "DOM", "DOM.Iterable", "ESNext"], "types": ["vite-plugin-solid-svg/types-component-solid", "vite/client", "vinxi/types/client"], "isolatedModules": true}, "tasks": {"dev": "vinxi dev", "build": "vinxi build", "start": "vinxi start"}}
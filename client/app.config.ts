import denoPlugin from '@deno/vite-plugin'
import type { BlobType } from '@massimo-erp/helpers'
import { defineConfig } from '@solidjs/start/config'
import tailwindcss from '@tailwindcss/vite'
import solidSvg from 'vite-plugin-solid-svg'

declare var process: BlobType

const isProduction =
  typeof Deno === 'undefined' ? process.env.NODE_ENV === 'production' : Deno.env.get('MODE') === 'production'

export default defineConfig({
  ssr: isProduction,
  devOverlay: !isProduction,
  vite: {
    plugins: [
      denoPlugin(),
      solidSvg(),
      tailwindcss() as BlobType,
    ],

    server: {
      fs: {
        allow: [
          '.',
          '../packages',
        ],
      },
    },
  },
  publicDir: '../packages/public',
})

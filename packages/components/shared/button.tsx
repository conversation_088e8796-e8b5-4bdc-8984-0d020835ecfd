import { Button as $Button } from '@kobalte/core/button'
import { cva, type VariantProps } from '@massimo-erp/helpers'
import type { JSX, ParentComponent } from 'solid-js'

export const createButtonVariants = cva(
  [
    'flex flex-row justify-between', // flex
    'border', // border
    'transition duration-300 ease-magic', // transition
    'text-center cursor-pointer', // other
  ],
  {
    variants: {
      size: {
        none: '',
        normal: 'text-sm px-8 py-4 rounded-xl',
        medium: 'text-md px-11 py-5 rounded-2xl',
        'icon-small': 'p-10',
      },

      thickness: {
        thin: 'font-thin',
        extralight: 'font-extralight',
        light: 'font-light',
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
        extrabold: 'font-extrabold',
        black: 'font-black',
      },

      color: {
        white: 'text-white dark:text-black/90',
        black: 'text-black/90 dark:text-white',
        primary: 'text-primary dark:text-primary-dark',
        secondary: 'text-secondary dark:text-secondary-dark',
      },

      bgColor: {
        white: 'bg-white dark:bg-black/90',
        black: 'bg-black/90 dark:bg-white',
        primary: 'bg-primary dark:bg-primary-dark',
        secondary: 'bg-secondary dark:bg-secondary-dark',
        ghost: 'bg-black/5 dark:bg-white/5',
      },

      border: {
        none: 'border-none',
        white: 'border-white dark:border-black',
        black: 'border-black dark:border-white',
        primary: 'border-primary dark:border-primary-dark',
        secondary: 'border-secondary dark:border-secondary-dark',
      },

      effect: {
        none: '',
        normal: 'hover:bg-black/10 active:bg-black/15 active:scale-105 dark:hover:bg-white/10 dark:active:bg-white/15',
        primary: 'hover:bg-primary/90 active:bg-primary dark:hover:bg-primary-dark/90 dark:active:bg-primary-dark',
      },
    },

    presets: {
      normal: {
        size: 'normal',
        thickness: 'normal',
        color: 'black',
        bgColor: 'ghost',
        border: 'none',
        effect: 'normal',
      },

      primary: {
        size: 'normal',
        thickness: 'normal',
        color: 'white',
        bgColor: 'primary',
        border: 'none',
        effect: 'primary',
      },
    },
  },
)

export type BaseButtonProps = VariantProps<typeof createButtonVariants>
export type ButtonProps = BaseButtonProps & {
  as?: 'button'
} & JSX.HTMLElementTags['button']

export const Button: ParentComponent<ButtonProps> = ({
  // Component
  as,
  children,

  // Styling
  size,
  thickness,
  color,
  bgColor,
  border,
  effect,

  class: cls,
  preset = 'normal',

  // HTML Attributes
  ...props
}) => {
  const styles = createButtonVariants({
    size,
    thickness,
    color,
    bgColor,
    border,
    effect,

    class: cls,
    preset,
  })

  return (
    <$Button
      class={styles}
      {...props}>
      {children}
    </$Button>
  )
}

import { cva, type VariantProps } from '@massimo-erp/helpers'
import type { JSX, ParentComponent } from 'solid-js'
import { Dynamic } from 'solid-js/web'

export const createTextVariants = cva(
  [
    'transition-color duration-700 ease-magic', // transition
  ],
  {
    variants: {
      size: {
        xs: 'text-xs',
        sm: 'text-sm',
        md: 'text-md',
        lg: 'text-lg',
        xl: 'text-xl',
        '2xl': 'text-2xl',
        '3xl': 'text-3xl',
      },

      thickness: {
        thin: 'font-thin',
        extralight: 'font-extralight',
        light: 'font-light',
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
        extrabold: 'font-extrabold',
        black: 'font-black',
      },

      decoration: {
        none: 'no-underline',
        underline: 'underline',
        lineThrough: 'line-through',
      },

      color: {
        white: 'text-white dark:text-black',
        black: 'text-black dark:text-white',
        primary: 'text-primary dark:text-primary-dark',
        secondary: 'text-secondary dark:text-secondary-dark',
      },

      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
        justify: 'text-justify',
      },
    },

    presets: {
      normal: {
        size: 'md',
        thickness: 'normal',
        decoration: 'none',
        color: 'black',
        align: 'left',
      },
    },
  },
)

export type BaseTextProps = VariantProps<typeof createTextVariants>
export type TextProps = BaseTextProps &
  (
    | ({
        as?: 'p'
      } & JSX.HTMLElementTags['p'])
    | ({
        as?: 'span'
      } & JSX.HTMLElementTags['span'])
    | ({
        as?: 'label'
      } & JSX.HTMLElementTags['label'])
  )

export const Text: ParentComponent<TextProps> = ({
  // Component
  as,
  children,

  // Styling
  size,
  thickness,
  decoration,
  color,
  align,

  class: cls,
  preset,

  // HTML Attributes
  ...props
}) => {
  const styles = createTextVariants({
    size,
    thickness,
    decoration,
    color,
    align,

    class: cls,
    preset,
  })
  const El = as ?? 'p'

  return (
    <Dynamic
      component={El}
      class={styles}
      {...props}>
      {children}
    </Dynamic>
  )
}

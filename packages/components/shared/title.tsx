import { cva, type VariantProps } from '@massimo-erp/helpers'
import type { JSX, ParentComponent } from 'solid-js'
import { Dynamic } from 'solid-js/web'

export const createTitleVariants = cva(
  [
    'transition-color duration-700 ease-magic', // transition
  ],
  {
    variants: {
      size: {
        xs: 'text-xs',
        sm: 'text-sm',
        md: 'text-md',
        lg: 'text-lg',
        xl: 'text-xl',
        '2xl': 'text-2xl',
        '3xl': 'text-3xl',
      },

      thickness: {
        thin: 'font-thin',
        extralight: 'font-extralight',
        light: 'font-light',
        normal: 'font-normal',
        medium: 'font-medium',
        semibold: 'font-semibold',
        bold: 'font-bold',
        extrabold: 'font-extrabold',
        black: 'font-black',
      },
      color: {
        white: 'text-white dark:text-black',
        black: 'text-black dark:text-white',
      },
      decoration: {
        none: 'no-underline',
        underline: 'underline',
        lineThrough: 'line-through',
      },
      align: {
        left: 'text-left',
        center: 'text-center',
        right: 'text-right',
        justify: 'text-justify',
      },
    },

    presets: {
      h1: {
        size: '3xl',
        thickness: 'bold',
      },
      h2: {
        size: '2xl',
        thickness: 'bold',
      },
      h3: {
        size: 'xl',
        thickness: 'bold',
      },
      h4: {
        size: 'lg',
        thickness: 'semibold',
      },
      h5: {
        size: 'md',
        thickness: 'semibold',
      },
      h6: {
        size: 'sm',
        thickness: 'semibold',
      },
    },
  },
)

export type BaseTitleProps = VariantProps<typeof createTitleVariants>

export type TitleProps = BaseTitleProps & {
  as?: `h${1 | 2 | 3 | 4 | 5 | 6}`
} & JSX.HTMLAttributes<HTMLHeadingElement>

export const Title: ParentComponent<TitleProps> = ({
  // Component
  as,
  children,

  // Styling
  size,
  thickness,
  color,
  align,
  decoration,

  class: cls,
  preset = 'h1',

  // HTML Attributes
  ...props
}) => {
  const styles = createTitleVariants({
    size,
    thickness,
    color,
    align,
    decoration,

    class: cls,
    preset,
  })
  const El = as ?? 'h1'

  return (
    <Dynamic
      component={El}
      class={styles}
      {...props}>
      {children}
    </Dynamic>
  )
}

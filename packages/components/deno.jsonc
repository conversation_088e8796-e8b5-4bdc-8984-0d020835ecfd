{"name": "@massimo-erp/components", "version": "0.0.1", "exports": {"./shared": "./shared/mod.ts", "./complex": "./complex/mod.ts"}, "imports": {"@solidjs/router": "npm:@solidjs/router@latest", "solid-js": "npm:solid-js@latest", "@kobalte/core": "npm:@kobalte/core@latest"}, "compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "preserve", "jsxImportSource": "solid-js", "allowJs": true, "noEmit": true, "strict": true, "lib": ["deno.ns", "DOM", "DOM.Iterable", "ESNext"], "isolatedModules": true}}
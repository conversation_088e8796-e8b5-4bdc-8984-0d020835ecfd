@import "tailwindcss";

@custom-variant dark (&:where(.dark, .dark *));
@theme {
  --\*: initial;

  --spacing: 0.125rem;

  --breakpoint-xs: 26.25rem;
  --breakpoint-sm: 40rem;
  --breakpoint-md: 48rem;
  --breakpoint-lg: 64rem;
  --breakpoint-xl: 80rem;
  --breakpoint-2xl: 96rem;

  --aspect-video: 16 / 9;

  --font-lufga: "lufga", sans-serif;

  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  --text-2xs: 0.625rem;
  --text-2xs--line-height: 0.825rem;
  --text-xs: 0.75rem;
  --text-xs--line-height: 1rem;
  --text-sm: 0.875rem;
  --text-sm--line-height: 1.25rem;
  --text-md: 1rem;
  --text-md--line-height: 1.5rem;
  --text-lg: 1.125rem;
  --text-lg--line-height: 1.75rem;
  --text-xl: 1.25rem;
  --text-xl--line-height: 1.75rem;
  --text-2xl: 1.5rem;
  --text-2xl--line-height: 2rem;
  --text-3xl: 1.875rem;
  --text-3xl--line-height: 2.25rem;

  --radius-xs: 0.125rem;
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-4xl: 2rem;
  --radius-full: 100%;
  --radius-max: 2.097rem; /* 33554400px max css value */

  --color-transparent: transparent;
  --color-pure-white: #fff;
  --color-pure-black: #000;

  --color-white: #e4e4e4;
  --color-black: #10141a;

  --color-primary: #83a2db;
  --color-secondary: #ce6969;
  --color-primary-dark: #22427e;
  --color-secondary-dark: #6e2e2e;

  --ease-magic: cubic-bezier(0.22, 1, 0.36, 1);
}

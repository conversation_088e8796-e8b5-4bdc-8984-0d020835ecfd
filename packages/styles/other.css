@import "./theme.css";

@layer components {
  .blurry-balls {
    --blur: 10vw;

    @apply blur-(--blur) grid h-full w-full overflow-hidden pointer-events-none -z-10 fixed transition-opacity duration-700 ease-magic;
  }

  .blurry-balls * {
    @apply row-span-full col-span-full;
  }

  .blurry-balls .shape {
    --width: 100%;
    --scale: 1;
    --opacity: 0.66;
    --top: 0;
    --left: 0;
    --path: polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%);

    --offset: 0deg;
    --speed: 120000ms;

    clip-path: var(--path);
    background: var(--background);
    animation: turn var(--speed) 300ms linear forwards infinite;

    @apply my-0 mx-auto aspect-square relative mix-blend-difference transition-[background] duration-700 ease-magic;
    @apply w-(--width) h-auto scale-(--scale) opacity-(--opacity) top-(--top) left-(--left) rotate-(--offset);
  }

  .light .blurry-balls .shape {
    --background: linear-gradient(
      var(--color-primary),
      var(--color-primary),
      var(--color-primary),
      var(--color-primary)
    );
  }

  .dark .blurry-balls .shape {
    --background: linear-gradient(
      var(--color-primary-dark),
      var(--color-primary-dark),
      var(--color-primary-dark),
      var(--color-primary-dark)
    );
  }

  @keyframes turn {
    to {
      rotate: calc(var(--offset) + 1turn);
    }
  }
}

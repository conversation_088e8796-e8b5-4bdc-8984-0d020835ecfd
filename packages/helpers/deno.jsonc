{"name": "@massimo-erp/helpers", "version": "0.0.1", "exports": "./mod.ts", "imports": {"solid-js": "npm:solid-js@latest", "class-variance-authority": "npm:class-variance-authority@latest", "clsx": "npm:clsx@latest", "@solidjs/meta": "npm:@solidjs/meta@latest", "@solid-primitives/cookies": "npm:@solid-primitives/cookies@^0.0.2"}, "compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "preserve", "jsxImportSource": "solid-js", "allowJs": true, "noEmit": true, "strict": true, "lib": ["deno.ns", "DOM", "DOM.Iterable", "ESNext"], "types": ["vite-plugin-solid-svg/types-component-solid", "vite/client", "vinxi/types/playground"], "isolatedModules": true}}
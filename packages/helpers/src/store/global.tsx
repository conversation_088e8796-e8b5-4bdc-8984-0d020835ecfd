import {
  createContext,
  // @ts-types="solid-js"
  createEffect,
  // @ts-types="solid-js"
  createRenderEffect,
  type ParentComponent,
} from 'solid-js'
import { createStore } from 'solid-js/store'
import { isServer } from 'solid-js/web'
import { cookieStore } from '../cookie.ts'

export type Lang = 'tr' | 'en'
export type Theme = 'dark' | 'light'

export interface GlobalStore {
  lang?: Lang | undefined
  theme?: Theme | undefined
  user?: null | undefined
}

export const DEFAULT_LANG: Lang = 'tr'
export const DEFAULT_THEME: Theme = 'dark'

const [globalStore, setGlobalStore] = createStore({
  lang: DEFAULT_LANG as Lang,
  setLang: (lang?: Lang) => {
    if (!isServer) {
      cookieStore.set('ozaco-lang', lang || DEFAULT_LANG, {
        path: '/',
        expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      })
    }

    setGlobalStore('lang', lang || DEFAULT_LANG)
  },

  theme: DEFAULT_THEME as Theme,
  setTheme: (theme?: Theme) => {
    if (!isServer) {
      cookieStore.set('ozaco-theme', theme || DEFAULT_THEME, {
        path: '/',
        expires: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year
      })
    }

    setGlobalStore('theme', theme || DEFAULT_THEME)
  },
  toggleTheme: () => {
    globalStore.setTheme(globalStore.theme === 'dark' ? 'light' : 'dark')
  },

  initialize: () => {
    const theme = cookieStore.get('ozaco-theme') as Theme
    const lang = cookieStore.get('ozaco-lang') as Lang

    globalStore.setLang(lang || DEFAULT_LANG)
    globalStore.setTheme(theme || DEFAULT_THEME)
  },

  user: null,
})

export const GlobalStoreContext = createContext(globalStore)

export const GlobalStoreProvider: ParentComponent = props => {
  if (isServer) {
    globalStore.initialize()

    console.log(`[server] Theme: ${globalStore.theme}, Lang: ${globalStore.lang}, isServer: ${isServer}`)
  }

  createEffect(() => {
    globalStore.initialize()
  })

  createRenderEffect(() => {
    if (isServer) return

    document.documentElement.lang = globalStore.lang
    document.documentElement.classList.toggle('dark', globalStore.theme === 'dark')
    document.documentElement.classList.toggle('light', globalStore.theme === 'light')

    console.log(
      `[client-createRenderEffect]: Theme: ${globalStore.theme}, Lang: ${globalStore.lang}, isServer: ${isServer}`,
    )
  })

  return <GlobalStoreContext.Provider value={globalStore}>{props.children}</GlobalStoreContext.Provider>
}

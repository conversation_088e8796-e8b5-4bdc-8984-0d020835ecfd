import { parseCookie } from '@solid-primitives/cookies'
import { getRequestEvent, isServer } from 'solid-js/web'

export const cookieStore = {
  set: (
    name: string,
    value: string,
    options?: {
      expires?: Date
      path?: string
      domain?: string
      secure?: boolean
      sameSite?: 'strict' | 'lax' | 'none'
    },
  ) => {
    if (isServer) return

    let cookieString = `${name}=${value}`

    if (options?.expires) {
      cookieString += `; expires=${options.expires.toUTCString()}`
    }
    if (options?.path) {
      cookieString += `; path=${options.path}`
    }
    if (options?.domain) {
      cookieString += `; domain=${options.domain}`
    }
    if (options?.secure) {
      cookieString += `; secure`
    }
    if (options?.sameSite) {
      cookieString += `; samesite=${options.sameSite}`
    }

    // biome-ignore lint/suspicious/noDocumentCookie: Redundant
    document.cookie = cookieString
  },

  get: (name: string, defaultValue?: string) => {
    const event = getRequestEvent()

    let cookieString = ''
    let target: string | null = null

    if (isServer && event) {
      cookieString = event.request.headers.get('cookie') || ''

      target = parseCookie(cookieString, name) || null
    } else {
      cookieString = document.cookie

      target = parseCookie(cookieString, name) || null
    }

    return target || defaultValue || null
  },
}

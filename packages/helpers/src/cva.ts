import { cva as $cva } from 'class-variance-authority'
import type { ClassValue } from 'clsx'
import type { BlobType, EmptyType, OmitUndefined, StringToBoolean } from './types.ts'

type ConfigSchema = Record<string, Record<string, ClassValue>>

type ConfigVariants<T extends ConfigSchema> = {
  [Variant in keyof T]?: StringToBoolean<keyof T[Variant]> | null | undefined
}
type ConfigVariantsMulti<T extends ConfigSchema> = {
  [Variant in keyof T]?: StringToBoolean<keyof T[Variant]> | StringToBoolean<keyof T[Variant]>[] | undefined
}

type Config<T, P> = T extends ConfigSchema
  ? {
      variants?: T
      defaultVariants?: ConfigVariants<T>
      compoundVariants?: (T extends ConfigSchema
        ? (ConfigVariants<T> | ConfigVariantsMulti<T>) & ClassProp
        : ClassProp)[]
      presets?: P
    }
  : never

type Props<T, P> = T extends ConfigSchema
  ? ConfigVariants<T> &
      ClassProp &
      (P extends never
        ? EmptyType
        : {
            preset?: keyof P | undefined
          })
  : ClassProp &
      (P extends never
        ? EmptyType
        : {
            preset?: keyof P | undefined
          })

export type ClassProp =
  | {
      class: ClassValue
      className?: never
    }
  | {
      class?: never
      className: ClassValue
    }
  | {
      class?: never
      className?: never
    }

export const cva = <T, P>(base?: ClassValue, config?: Config<T, P>) => {
  const fn = $cva(base, config as BlobType)

  return (rawProps: Props<NoInfer<T>, NoInfer<P>>) => {
    const props: BlobType = {}

    for (const key in rawProps) {
      if (rawProps[key]) {
        props[key] = rawProps[key]
      }
    }

    if (config?.presets && (props as BlobType).preset) {
      return fn(Object.assign({}, config.presets[(props as BlobType).preset as keyof typeof config.presets], props))
    }

    return fn(props as BlobType)
  }
}

export type VariantProps<Component extends (...args: BlobType) => BlobType> = Omit<
  OmitUndefined<Parameters<Component>[0]>,
  'class' | 'className'
>
export { clsx } from 'clsx'

{"$schema": "https://biomejs.dev/schemas/2.2.4/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "bracketSameLine": true, "expand": "always", "attributePosition": "multiline", "lineWidth": 120, "bracketSpacing": true, "lineEnding": "lf", "useEditorconfig": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noUnknownAtRules": "off"}}, "domains": {"solid": "recommended", "project": "recommended"}}, "javascript": {"formatter": {"quoteStyle": "single", "jsxQuoteStyle": "double", "semicolons": "asNeeded", "quoteProperties": "asNeeded", "arrowParentheses": "asNeeded", "trailingCommas": "all", "attributePosition": "multiline", "operatorLinebreak": "after"}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}
// Test Relationships for Massimo ERP
// These relationships demonstrate the permission model

// Organization: Massimo Corp
organization:massimo_corp#owner@user:ceo
organization:massimo_corp#admin@user:it_admin
organization:massimo_corp#manager@user:dept_manager
organization:massimo_corp#employee@user:john_doe
organization:massimo_corp#employee@user:jane_smith
organization:massimo_corp#accountant@user:accountant_alice
organization:massimo_corp#viewer@user:intern_bob

// Department: Engineering
department:engineering#organization@organization:massimo_corp
department:engineering#head@user:dept_manager
department:engineering#manager@user:eng_manager
department:engineering#employee@user:john_doe
department:engineering#employee@user:jane_smith
department:engineering#viewer@user:intern_bob

// Department: Finance
department:finance#organization@organization:massimo_corp
department:finance#head@user:finance_head
department:finance#manager@user:finance_manager
department:finance#employee@user:accountant_alice
department:finance#viewer@user:auditor_charlie

// Team: Frontend Team
team:frontend_team#organization@organization:massimo_corp
team:frontend_team#department@department:engineering
team:frontend_team#lead@user:john_doe
team:frontend_team#member@user:jane_smith
team:frontend_team#member@user:frontend_dev

// Team: Backend Team
team:backend_team#organization@organization:massimo_corp
team:backend_team#department@department:engineering
team:backend_team#lead@user:backend_lead
team:backend_team#member@user:backend_dev1
team:backend_team#member@user:backend_dev2

// Project: ERP System
project:erp_system#organization@organization:massimo_corp
project:erp_system#department@department:engineering
project:erp_system#team@team:frontend_team
project:erp_system#owner@user:dept_manager
project:erp_system#manager@user:eng_manager
project:erp_system#contributor@user:john_doe
project:erp_system#contributor@user:jane_smith
project:erp_system#viewer@user:intern_bob

// Project: Financial Dashboard
project:financial_dashboard#organization@organization:massimo_corp
project:financial_dashboard#department@department:finance
project:financial_dashboard#owner@user:finance_head
project:financial_dashboard#manager@user:finance_manager
project:financial_dashboard#contributor@user:accountant_alice
project:financial_dashboard#viewer@user:auditor_charlie

// Document: API Documentation
document:api_docs#organization@organization:massimo_corp
document:api_docs#department@department:engineering
document:api_docs#project@project:erp_system
document:api_docs#owner@user:john_doe
document:api_docs#editor@user:jane_smith
document:api_docs#commenter@user:eng_manager
document:api_docs#viewer@user:intern_bob

// Document: Financial Report Q1
document:financial_report_q1#organization@organization:massimo_corp
document:financial_report_q1#department@department:finance
document:financial_report_q1#owner@user:accountant_alice
document:financial_report_q1#editor@user:finance_manager
document:financial_report_q1#viewer@user:finance_head
document:financial_report_q1#viewer@user:ceo

// Financial Record: Office Rent
financial_record:office_rent_jan#organization@organization:massimo_corp
financial_record:office_rent_jan#department@department:finance
financial_record:office_rent_jan#creator@user:accountant_alice
financial_record:office_rent_jan#approver@user:finance_manager
financial_record:office_rent_jan#auditor@user:auditor_charlie
financial_record:office_rent_jan#viewer@user:finance_head

// Financial Record: Software Licenses
financial_record:software_licenses#organization@organization:massimo_corp
financial_record:software_licenses#department@department:engineering
financial_record:software_licenses#creator@user:it_admin
financial_record:software_licenses#approver@user:dept_manager
financial_record:software_licenses#viewer@user:ceo

// Invoice: Client Project Invoice
invoice:client_invoice_001#organization@organization:massimo_corp
invoice:client_invoice_001#department@department:finance
invoice:client_invoice_001#creator@user:accountant_alice
invoice:client_invoice_001#approver@user:finance_manager
invoice:client_invoice_001#viewer@user:finance_head

// Report: Monthly Performance Report
report:monthly_performance#organization@organization:massimo_corp
report:monthly_performance#department@department:engineering
report:monthly_performance#creator@user:eng_manager
report:monthly_performance#viewer@user:dept_manager
report:monthly_performance#viewer@user:ceo

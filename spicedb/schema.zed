// Massimo ERP Authorization Schema
// Based on Google Zanzibar model for fine-grained permissions

// User definition - represents system users
definition user {}

// Organization definition - top-level entity (company)
definition organization {
    // Direct relations
    relation owner: user                    // Company owner
    relation admin: user                    // System administrators
    relation manager: user                  // Department managers
    relation employee: user                 // Regular employees
    relation accountant: user               // Financial access
    relation viewer: user                   // Read-only access

    // Computed permissions
    permission view = viewer + employee + accountant + manager + admin + owner
    permission edit = employee + accountant + manager + admin + owner
    permission manage = manager + admin + owner
    permission admin_access = admin + owner
    permission financial_access = accountant + admin + owner
    permission full_control = owner
}

// Department definition - organizational units
definition department {
    // Relations
    relation organization: organization     // Parent organization
    relation head: user                     // Department head
    relation manager: user                  // Department managers
    relation employee: user                 // Department employees
    relation viewer: user                   // Department viewers

    // Computed permissions with inheritance
    permission view = viewer + employee + manager + head + organization->view
    permission edit = employee + manager + head + organization->edit
    permission manage = manager + head + organization->manage
    permission admin_access = head + organization->admin_access
}

// Team definition - smaller work groups within departments
definition team {
    // Relations
    relation department: department         // Parent department
    relation organization: organization     // Parent organization
    relation lead: user                     // Team lead
    relation member: user                   // Team members
    relation viewer: user                   // Team viewers

    // Computed permissions
    permission view = viewer + member + lead + department->view + organization->view
    permission edit = member + lead + department->edit + organization->edit
    permission manage = lead + department->manage + organization->manage
}

// Project definition - work projects
definition project {
    // Relations
    relation organization: organization     // Parent organization
    relation department: department         // Parent department
    relation team: team                     // Assigned team
    relation owner: user                    // Project owner
    relation manager: user                  // Project managers
    relation contributor: user              // Project contributors
    relation viewer: user                   // Project viewers

    // Computed permissions
    permission view = viewer + contributor + manager + owner + team->view + department->view + organization->view
    permission edit = contributor + manager + owner + team->edit + department->edit + organization->edit
    permission manage = manager + owner + team->manage + department->manage + organization->manage
    permission delete = owner + department->admin_access + organization->admin_access
}

// Document definition - files and documents
definition document {
    // Relations
    relation organization: organization     // Parent organization
    relation department: department         // Parent department
    relation project: project               // Parent project
    relation owner: user                    // Document owner
    relation editor: user                   // Can edit document
    relation commenter: user                // Can comment only
    relation viewer: user                   // Can view only

    // Computed permissions
    permission view = viewer + commenter + editor + owner + project->view + department->view + organization->view
    permission comment = commenter + editor + owner + project->edit + department->edit + organization->edit
    permission edit = editor + owner + project->edit + department->edit + organization->edit
    permission delete = owner + project->manage + department->admin_access + organization->admin_access
    permission share = owner + project->manage + department->manage + organization->manage
}

// Financial Record definition - accounting and financial data
definition financial_record {
    // Relations
    relation organization: organization     // Parent organization
    relation department: department         // Related department
    relation creator: user                  // Record creator
    relation approver: user                 // Can approve transactions
    relation auditor: user                  // Can audit records
    relation viewer: user                   // Can view records

    // Computed permissions
    permission view = viewer + creator + approver + auditor + organization->financial_access + department->view
    permission edit = creator + organization->financial_access
    permission approve = approver + organization->financial_access
    permission audit = auditor + organization->admin_access
    permission delete = organization->admin_access
}

// Invoice definition - billing documents
definition invoice {
    // Relations
    relation organization: organization     // Parent organization
    relation department: department         // Billing department
    relation creator: user                  // Invoice creator
    relation approver: user                 // Can approve invoice
    relation viewer: user                   // Can view invoice

    // Computed permissions
    permission view = viewer + creator + approver + organization->financial_access + department->view
    permission edit = creator + organization->financial_access
    permission approve = approver + organization->financial_access
    permission send = approver + organization->financial_access
    permission delete = organization->admin_access
}

// Report definition - business reports
definition report {
    // Relations
    relation organization: organization     // Parent organization
    relation department: department         // Related department
    relation creator: user                  // Report creator
    relation viewer: user                   // Can view report

    // Computed permissions
    permission view = viewer + creator + department->view + organization->view
    permission edit = creator + department->edit + organization->edit
    permission delete = creator + department->admin_access + organization->admin_access
    permission export = viewer + creator + department->manage + organization->manage
}
# Permission Test Scenarios

Bu dosya Zanzibar schema'mızın doğru çalışıp çalışmadığını test etmek için senaryolar içerir.

## Test Kullanıcıları

- `ceo`: Company owner
- `it_admin`: IT administrator  
- `dept_manager`: Department manager
- `eng_manager`: Engineering manager
- `finance_head`: Finance department head
- `finance_manager`: Finance manager
- `john_doe`: Engineering employee
- `jane_smith`: Engineering employee
- `accountant_alice`: Finance employee
- `intern_bob`: Intern with viewer access
- `auditor_charlie`: External auditor

## Test Senaryoları

### 1. Organization Level Permissions

**CEO (owner) should have full access:**
```
✅ ceo can view organization:massimo_corp
✅ ceo can edit organization:massimo_corp  
✅ ceo can manage organization:massimo_corp
✅ ceo can admin_access organization:massimo_corp
✅ ceo can financial_access organization:massimo_corp
✅ ceo can full_control organization:massimo_corp
```

**IT Admin should have admin access:**
```
✅ it_admin can view organization:massimo_corp
✅ it_admin can edit organization:massimo_corp
✅ it_admin can manage organization:massimo_corp
✅ it_admin can admin_access organization:massimo_corp
✅ it_admin can financial_access organization:massimo_corp
❌ it_admin cannot full_control organization:massimo_corp
```

**Regular employee should have limited access:**
```
✅ john_doe can view organization:massimo_corp
✅ john_doe can edit organization:massimo_corp
❌ john_doe cannot manage organization:massimo_corp
❌ john_doe cannot admin_access organization:massimo_corp
❌ john_doe cannot financial_access organization:massimo_corp
```

### 2. Department Level Permissions

**Department head should manage their department:**
```
✅ dept_manager can view department:engineering
✅ dept_manager can edit department:engineering
✅ dept_manager can manage department:engineering
✅ dept_manager can admin_access department:engineering
```

**Department employee should have limited access:**
```
✅ john_doe can view department:engineering
✅ john_doe can edit department:engineering
❌ john_doe cannot manage department:engineering
❌ john_doe cannot admin_access department:engineering
```

**Cross-department access should be limited:**
```
❌ john_doe cannot view department:finance
❌ john_doe cannot edit department:finance
```

### 3. Project Level Permissions

**Project owner should control project:**
```
✅ dept_manager can view project:erp_system
✅ dept_manager can edit project:erp_system
✅ dept_manager can manage project:erp_system
✅ dept_manager can delete project:erp_system
```

**Project contributor should edit but not manage:**
```
✅ john_doe can view project:erp_system
✅ john_doe can edit project:erp_system
❌ john_doe cannot manage project:erp_system
❌ john_doe cannot delete project:erp_system
```

### 4. Document Level Permissions

**Document owner should control document:**
```
✅ john_doe can view document:api_docs
✅ john_doe can edit document:api_docs
✅ john_doe can delete document:api_docs
✅ john_doe can share document:api_docs
```

**Document editor should edit but not delete:**
```
✅ jane_smith can view document:api_docs
✅ jane_smith can edit document:api_docs
❌ jane_smith cannot delete document:api_docs
✅ jane_smith can comment document:api_docs
```

**Document commenter should only comment:**
```
✅ eng_manager can view document:api_docs
✅ eng_manager can comment document:api_docs
❌ eng_manager cannot edit document:api_docs
```

### 5. Financial Record Permissions

**Accountant should create and edit financial records:**
```
✅ accountant_alice can view financial_record:office_rent_jan
✅ accountant_alice can edit financial_record:office_rent_jan
❌ accountant_alice cannot approve financial_record:office_rent_jan
❌ accountant_alice cannot delete financial_record:office_rent_jan
```

**Finance manager should approve:**
```
✅ finance_manager can view financial_record:office_rent_jan
❌ finance_manager cannot edit financial_record:office_rent_jan
✅ finance_manager can approve financial_record:office_rent_jan
❌ finance_manager cannot delete financial_record:office_rent_jan
```

**Auditor should audit:**
```
✅ auditor_charlie can view financial_record:office_rent_jan
❌ auditor_charlie cannot edit financial_record:office_rent_jan
❌ auditor_charlie cannot approve financial_record:office_rent_jan
✅ auditor_charlie can audit financial_record:office_rent_jan
```

**Non-finance employee should not access:**
```
❌ john_doe cannot view financial_record:office_rent_jan
❌ john_doe cannot edit financial_record:office_rent_jan
```

### 6. Inheritance Tests

**Organization permissions should inherit to departments:**
```
✅ ceo can view department:engineering (via organization inheritance)
✅ ceo can manage department:engineering (via organization inheritance)
```

**Department permissions should inherit to projects:**
```
✅ dept_manager can view project:erp_system (via department inheritance)
✅ dept_manager can manage project:erp_system (via department inheritance)
```

**Project permissions should inherit to documents:**
```
✅ dept_manager can view document:api_docs (via project inheritance)
✅ dept_manager can delete document:api_docs (via project inheritance)
```

## Test Commands

SpiceDB çalışırken bu komutlarla test edebilirsiniz:

```bash
# Permission check example
zed permission check document:api_docs view user:john_doe --endpoint localhost:50051 --insecure

# Relationship lookup example  
zed relationship read --endpoint localhost:50051 --insecure
```
